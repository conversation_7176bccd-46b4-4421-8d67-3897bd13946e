-- CreateEnum
CREATE TYPE "public"."SandboxTransactionStatus" AS ENUM ('PENDING', 'SUCCESSFUL', 'FAILED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "public"."SandboxTransactionType" AS ENUM ('COLLECTION');

-- CreateTable
CREATE TABLE "public"."sandbox_api_keys" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "key_name" TEXT NOT NULL,
    "key_hash" TEXT NOT NULL,
    "environment" TEXT NOT NULL DEFAULT 'sandbox',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "usage_count" INTEGER NOT NULL DEFAULT 0,
    "rate_limit" INTEGER NOT NULL DEFAULT 100,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_used_at" TIMESTAMP(3),

    CONSTRAINT "sandbox_api_keys_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."sandbox_transactions" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "api_key_id" UUID,
    "external_id" TEXT NOT NULL,
    "amount" DECIMAL(15,2) NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'XAF',
    "type" "public"."SandboxTransactionType" NOT NULL,
    "status" "public"."SandboxTransactionStatus" NOT NULL DEFAULT 'PENDING',
    "mtn_transaction_id" TEXT,
    "mtn_reference_id" TEXT NOT NULL,
    "payer_phone" TEXT NOT NULL,
    "payer_message" TEXT,
    "payee_note" TEXT,
    "callback_url" TEXT,
    "failure_reason" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "completed_at" TIMESTAMP(3),

    CONSTRAINT "sandbox_transactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."sandbox_wallets" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "balance" DECIMAL(15,2) NOT NULL DEFAULT 100000,
    "currency" TEXT NOT NULL DEFAULT 'XAF',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "sandbox_wallets_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "sandbox_transactions_mtn_reference_id_key" ON "public"."sandbox_transactions"("mtn_reference_id");

-- CreateIndex
CREATE UNIQUE INDEX "sandbox_wallets_userId_key" ON "public"."sandbox_wallets"("userId");

-- AddForeignKey
ALTER TABLE "public"."student_profiles" ADD CONSTRAINT "student_profiles_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."admin_profiles" ADD CONSTRAINT "admin_profiles_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
