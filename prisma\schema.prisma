generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("POSTGRES_URL_NON_POOLING")
  schemas   = ["public"]
}

model User {
  id             String          @id @default(uuid()) @db.Uuid
  role           UserType
  studentProfile StudentProfile?
  adminProfile   AdminProfile?

  @@map("users")
  @@schema("public")
}

model StudentProfile {
  id             String    @id @default(uuid()) @db.Uuid
  fullName       String    @map("full_name")
  email          String    @unique
  phoneNumber    String?   @map("phone_number")
  createdAt      DateTime  @default(now()) @map("created_at")
  kycStatus      KycStatus @default(NOT_STARTED) @map("kyc_status")
  kycCompletedAt DateTime? @map("kyc_completed_at")
  kycReviewedAt  DateTime? @map("kyc_reviewed_at")

  // relation back to central user
  userId String @unique @db.Uuid
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("student_profiles")
  @@schema("public")
}

model AdminProfile {
  id           String   @id @default(uuid()) @db.Uuid
  fullName     String   @map("full_name")
  email        String   @unique
  phoneNumber  String?
  location     String
  profession   String
  organization String?
  experience   String
  motivation   String
  availability String
  createdAt    DateTime @default(now()) @map("created_at")

  // relation back to central user
  userId String @unique @db.Uuid
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("admin_profiles")
  @@schema("public")
}

model sandbox_api_keys {
  id           String    @id @db.Uuid
  userId       String    @db.Uuid
  key_name     String
  key_hash     String
  environment  String    @default("sandbox")
  is_active    Boolean   @default(true)
  usage_count  Int       @default(0)
  rate_limit   Int       @default(100)
  created_at   DateTime  @default(now())
  last_used_at DateTime?

  @@schema("public")
}

model sandbox_transactions {
  id                 String                   @id @db.Uuid
  userId             String                   @db.Uuid
  api_key_id         String?                  @db.Uuid
  external_id        String
  amount             Decimal                  @db.Decimal(15, 2)
  currency           String                   @default("XAF")
  type               SandboxTransactionType
  status             SandboxTransactionStatus @default(PENDING)
  mtn_transaction_id String?
  mtn_reference_id   String                   @unique
  payer_phone        String
  payer_message      String?
  payee_note         String?
  callback_url       String?
  failure_reason     String?
  created_at         DateTime                 @default(now())
  updated_at         DateTime
  completed_at       DateTime?

  @@schema("public")
}

model sandbox_wallets {
  id         String   @id @db.Uuid
  userId     String   @unique @db.Uuid
  balance    Decimal  @default(100000) @db.Decimal(15, 2)
  currency   String   @default("XAF")
  created_at DateTime @default(now())
  updated_at DateTime

  @@schema("public")
}

enum UserType {
  STUDENT
  ADMIN

  @@schema("public")
}

enum KycStatus {
  NOT_STARTED
  IN_PROGRESS
  PENDING_REVIEW
  APPROVED
  REJECTED

  @@schema("public")
}

enum SandboxTransactionStatus {
  PENDING
  SUCCESSFUL
  FAILED
  CANCELLED

  @@schema("public")
}

enum SandboxTransactionType {
  COLLECTION

  @@schema("public")
}
