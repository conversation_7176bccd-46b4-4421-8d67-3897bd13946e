// Datasource points to Supabase Postgres
// The POSTGRES_URL env var is provided by Vercel (and locally via .env.local)
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("POSTGRES_URL_NON_POOLING")
  // Only manage our own public schema; Supabase manages "auth"
  schemas  = ["public"]
}

generator client {
  provider = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

model User {
  id       String   @id @default(uuid()) @db.Uuid
  role     UserType
  studentProfile  StudentProfile?
  adminProfile    AdminProfile?

  @@map("users")
  @@schema("public")
}

model StudentProfile {
  id       String   @id @default(uuid()) @db.Uuid
  fullName String   @map("full_name")
  email    String   @unique
  phoneNumber String? @map("phone_number")
  createdAt DateTime @default(now()) @map("created_at")
  kycStatus   KycStatus @default(NOT_STARTED) @map("kyc_status")
  kycCompletedAt DateTime? @map("kyc_completed_at")
  kycReviewedAt DateTime? @map("kyc_reviewed_at")

  // relation back to central user
  userId  String   @unique @db.Uuid
  user    User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("student_profiles")
  @@schema("public")
}

model AdminProfile {
  id           String   @id @default(uuid()) @db.Uuid
  fullName     String   @map("full_name")
  email        String   @unique
  phoneNumber  String?
  location     String
  profession   String
  organization String?
  experience   String
  motivation   String
  availability String
  createdAt    DateTime @default(now()) @map("created_at")

  // relation back to central user
  userId String @unique @db.Uuid
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("admin_profiles")
  @@schema("public")
}

enum UserType {
  @@schema("public")
  STUDENT
  ADMIN
}

enum KycStatus {
  @@schema("public")
  NOT_STARTED
  IN_PROGRESS
  PENDING_REVIEW
  APPROVED
  REJECTED
}
