import { createClient } from '@supabase/supabase-js'
import { PrismaClient } from '@prisma/client'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

const prisma = new PrismaClient()

async function restoreUsers() {
  try {
    console.log('🔍 Fetching auth users from Supabase...')
    
    // Get all auth users
    const { data: authUsers, error } = await supabase.auth.admin.listUsers()
    
    if (error) {
      console.error('Error fetching auth users:', error)
      return
    }

    console.log(`📊 Found ${authUsers.users.length} auth users`)

    for (const authUser of authUsers.users) {
      const email = authUser.email
      const userId = authUser.id

      if (!email) {
        console.log(`⚠️  Skipping user ${userId} - no email`)
        continue
      }

      console.log(`👤 Processing user: ${email}`)

      // Determine user role based on email pattern or metadata
      // You can adjust this logic based on your needs
      let userRole: 'STUDENT' | 'ADMIN' = 'STUDENT'
      
      // Check if user has admin role in metadata or email pattern
      if (authUser.user_metadata?.role === 'ADMIN' || 
          authUser.app_metadata?.role === 'ADMIN' ||
          email.includes('admin') || 
          email.includes('bless')) {
        userRole = 'ADMIN'
      }

      try {
        // Create user in your users table
        const user = await prisma.user.upsert({
          where: { id: userId },
          update: { role: userRole },
          create: {
            id: userId,
            role: userRole
          }
        })

        console.log(`✅ Created/updated user: ${email} as ${userRole}`)

        // Create profile based on role
        if (userRole === 'STUDENT') {
          await prisma.studentProfile.upsert({
            where: { userId: userId },
            update: {
              email: email,
              fullName: authUser.user_metadata?.full_name || email.split('@')[0]
            },
            create: {
              userId: userId,
              email: email,
              fullName: authUser.user_metadata?.full_name || email.split('@')[0],
              kycStatus: 'NOT_STARTED'
            }
          })
          console.log(`📝 Created student profile for: ${email}`)
        } else {
          await prisma.adminProfile.upsert({
            where: { userId: userId },
            update: {
              email: email,
              fullName: authUser.user_metadata?.full_name || email.split('@')[0]
            },
            create: {
              userId: userId,
              email: email,
              fullName: authUser.user_metadata?.full_name || email.split('@')[0],
              location: 'Unknown',
              profession: 'Unknown',
              experience: 'Unknown',
              motivation: 'Unknown',
              availability: 'Unknown'
            }
          })
          console.log(`👨‍💼 Created admin profile for: ${email}`)
        }

        // Create sandbox wallet for students
        if (userRole === 'STUDENT') {
          await prisma.sandbox_wallets.upsert({
            where: { userId: userId },
            update: {},
            create: {
              id: crypto.randomUUID(),
              userId: userId,
              balance: 100000,
              currency: 'XAF',
              updated_at: new Date()
            }
          })
          console.log(`💰 Created sandbox wallet for: ${email}`)
        }

      } catch (error) {
        console.error(`❌ Error processing user ${email}:`, error)
      }
    }

    console.log('🎉 User restoration completed!')
    
    // Show summary
    const userCount = await prisma.user.count()
    const studentCount = await prisma.studentProfile.count()
    const adminCount = await prisma.adminProfile.count()
    const walletCount = await prisma.sandbox_wallets.count()

    console.log('\n📊 Summary:')
    console.log(`- Users: ${userCount}`)
    console.log(`- Students: ${studentCount}`)
    console.log(`- Admins: ${adminCount}`)
    console.log(`- Sandbox Wallets: ${walletCount}`)

  } catch (error) {
    console.error('💥 Fatal error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
restoreUsers()
